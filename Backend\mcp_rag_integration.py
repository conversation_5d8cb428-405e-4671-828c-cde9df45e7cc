"""
MCP-RAG Integration Layer
Connects MCP AWS troubleshooting capabilities with the existing RAG system
"""

import asyncio
import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import subprocess
import os
from query import QueryEngine
from prompts import AWSPromptSelector

logger = logging.getLogger(__name__)

class MCPRAGIntegration:
    """Integration layer between MCP AWS server and RAG system"""
    
    def __init__(self, query_engine: QueryEngine):
        self.query_engine = query_engine
        self.prompt_selector = AWSPromptSelector()
        self.mcp_process = None
        
    async def start_mcp_server(self):
        """Start the MCP AWS server as a subprocess"""
        try:
            import sys
            import os

            # Use absolute path and proper Windows subprocess handling
            python_exe = sys.executable
            server_path = os.path.abspath("Backend/mcp_aws_server.py")

            # Start MCP server with Windows-compatible settings
            self.mcp_process = subprocess.Popen(
                [python_exe, server_path],
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=0,  # Unbuffered for Windows
                creationflags=subprocess.CREATE_NEW_PROCESS_GROUP if hasattr(subprocess, 'CREATE_NEW_PROCESS_GROUP') else 0
            )

            # Give the server a moment to start
            await asyncio.sleep(1)

            logger.info("MCP AWS server started successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to start MCP server: {e}")
            return False
    
    async def stop_mcp_server(self):
        """Stop the MCP AWS server"""
        if self.mcp_process:
            self.mcp_process.terminate()
            self.mcp_process.wait()
            logger.info("MCP AWS server stopped")
    
    async def send_mcp_request(self, method: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Send a request to the MCP server - simplified for Windows compatibility"""
        try:
            # For Windows compatibility, use direct AWS API calls instead of subprocess
            # This is more reliable than complex subprocess communication

            if method == "tools/call":
                tool_name = params.get("name")
                tool_args = params.get("arguments", {})

                if tool_name == "get_aws_inventory":
                    return await self._get_aws_inventory_direct()
                elif tool_name == "get_aws_costs":
                    return await self._get_aws_costs_direct()
                elif tool_name == "get_cloudwatch_metrics":
                    return await self._get_cloudwatch_metrics_direct(tool_args)
                else:
                    # Fallback for other tools
                    return {"result": {"status": "Tool not implemented in direct mode"}}

            return {"result": {"status": "Method not supported in direct mode"}}

        except Exception as e:
            logger.error(f"MCP request failed: {e}")
            return {"error": str(e)}
    
    async def enhanced_troubleshooting_query(self, question: str, context_type: str = "auto") -> Dict[str, Any]:
        """
        Enhanced query that combines RAG with real-time AWS data via MCP
        
        Args:
            question: User's troubleshooting question
            context_type: Type of AWS context to gather (auto, metrics, logs, instances)
        """
        try:
            # Step 1: Analyze the question to determine what AWS data to fetch
            aws_context_needed = self._analyze_question_for_aws_context(question)
            
            # Step 2: Fetch real-time AWS data via MCP
            aws_data = await self._fetch_aws_context(aws_context_needed, question)
            
            # Step 3: Get relevant documentation from RAG system
            rag_result = self.query_engine.query_advanced(
                question=question,
                retrieval_config={
                    "retriever_type": "multi_vector",
                    "use_compression": True,
                    "similarity_threshold": 0.3
                }
            )
            
            # Step 4: Combine AWS real-time data with RAG documentation
            enhanced_context = self._combine_contexts(rag_result, aws_data)
            
            # Step 5: Generate enhanced response using combined context
            enhanced_response = await self._generate_enhanced_response(
                question, enhanced_context, rag_result
            )
            
            return enhanced_response
            
        except Exception as e:
            logger.error(f"Enhanced troubleshooting query failed: {e}")
            # Fallback to regular RAG query
            return self.query_engine.query_advanced(question)
    
    def _analyze_question_for_aws_context(self, question: str) -> Dict[str, bool]:
        """Analyze question to determine what AWS context to fetch"""
        question_lower = question.lower()
        
        context_needed = {
            "metrics": any(term in question_lower for term in [
                "performance", "cpu", "memory", "disk", "network", "latency",
                "throughput", "utilization", "slow", "high", "spike"
            ]),
            "logs": any(term in question_lower for term in [
                "error", "exception", "fail", "crash", "bug", "issue",
                "log", "message", "stack trace", "debug"
            ]),
            "instances": any(term in question_lower for term in [
                "instance", "server", "ec2", "rds", "database", "service",
                "status", "health", "running", "stopped", "terminated"
            ]),
            "alarms": any(term in question_lower for term in [
                "alarm", "alert", "notification", "threshold", "breach",
                "monitoring", "watch", "trigger"
            ]),
            "inventory": any(term in question_lower for term in [
                "how many", "list all", "show me", "what instances", "what services",
                "current status", "running instances", "inventory", "count"
            ]),
            "costs": any(term in question_lower for term in [
                "cost", "costs", "billing", "spend", "spending", "expensive", "price",
                "budget", "bill", "charges", "optimization", "optimize"
            ])
        }
        
        return context_needed
    
    async def _fetch_aws_context(self, context_needed: Dict[str, bool], question: str) -> Dict[str, Any]:
        """Fetch relevant AWS context via MCP based on analysis"""
        aws_data = {}
        
        try:
            # Fetch CloudWatch metrics if needed
            if context_needed.get("metrics"):
                metrics_response = await self.send_mcp_request(
                    "tools/call",
                    {
                        "name": "get_cloudwatch_metrics",
                        "arguments": {
                            "namespace": "AWS/EC2",  # Default, could be made smarter
                            "metric_name": "CPUUtilization"
                        }
                    }
                )
                aws_data["metrics"] = metrics_response.get("result", {})
            
            # Fetch logs if needed
            if context_needed.get("logs"):
                # This would need log group name - could be configured or detected
                log_groups = ["/aws/lambda/your-function", "/aws/ec2/your-app"]  # Configure these
                for log_group in log_groups:
                    try:
                        logs_response = await self.send_mcp_request(
                            "tools/call",
                            {
                                "name": "query_cloudwatch_logs",
                                "arguments": {
                                    "log_group": log_group,
                                    "query": "ERROR"
                                }
                            }
                        )
                        aws_data.setdefault("logs", []).append(logs_response.get("result", {}))
                    except Exception as e:
                        logger.warning(f"Failed to fetch logs from {log_group}: {e}")
            
            # Fetch instance health if needed
            if context_needed.get("instances"):
                instances_response = await self.send_mcp_request(
                    "tools/call",
                    {
                        "name": "get_ec2_instance_health",
                        "arguments": {}
                    }
                )
                aws_data["instances"] = instances_response.get("result", {})
                
                # Also fetch RDS instances
                rds_response = await self.send_mcp_request(
                    "tools/call",
                    {
                        "name": "get_rds_instance_status",
                        "arguments": {}
                    }
                )
                aws_data["rds_instances"] = rds_response.get("result", {})
            
            # Fetch alarm information if needed
            if context_needed.get("alarms"):
                alarms_response = await self.send_mcp_request(
                    "tools/call",
                    {
                        "name": "analyze_error_patterns",
                        "arguments": {
                            "services": ["ec2", "rds", "lambda"],
                            "time_range_hours": 1
                        }
                    }
                )
                aws_data["alarms"] = alarms_response.get("result", {})

            # Fetch inventory information if needed
            if context_needed.get("inventory"):
                inventory_response = await self.send_mcp_request(
                    "tools/call",
                    {
                        "name": "get_aws_inventory",
                        "arguments": {}
                    }
                )
                aws_data["inventory"] = inventory_response.get("result", {})

            # Fetch cost information if needed
            if context_needed.get("costs"):
                costs_response = await self.send_mcp_request(
                    "tools/call",
                    {
                        "name": "get_aws_costs",
                        "arguments": {}
                    }
                )
                aws_data["costs"] = costs_response.get("result", {})

        except Exception as e:
            logger.error(f"Failed to fetch AWS context: {e}")
            aws_data["error"] = str(e)

        return aws_data
    
    def _combine_contexts(self, rag_result: Dict[str, Any], aws_data: Dict[str, Any]) -> str:
        """Combine RAG documentation with real-time AWS data"""
        combined_context = []
        
        # Add RAG documentation context
        if rag_result.get("sources"):
            combined_context.append("=== DOCUMENTATION CONTEXT ===")
            for i, source in enumerate(rag_result["sources"][:3]):  # Limit to top 3
                combined_context.append(f"Source {i+1}: {source.get('content_preview', '')}")
        
        # Add real-time AWS data
        if aws_data:
            combined_context.append("\n=== REAL-TIME AWS DATA ===")
            
            if "metrics" in aws_data:
                combined_context.append("CloudWatch Metrics:")
                combined_context.append(json.dumps(aws_data["metrics"], indent=2))
            
            if "logs" in aws_data:
                combined_context.append("Recent Error Logs:")
                for log_data in aws_data["logs"]:
                    combined_context.append(json.dumps(log_data, indent=2))
            
            if "instances" in aws_data:
                combined_context.append("EC2 Instance Status:")
                combined_context.append(json.dumps(aws_data["instances"], indent=2))
            
            if "rds_instances" in aws_data:
                combined_context.append("RDS Instance Status:")
                combined_context.append(json.dumps(aws_data["rds_instances"], indent=2))
            
            if "alarms" in aws_data:
                combined_context.append("Active Alarms and Patterns:")
                combined_context.append(json.dumps(aws_data["alarms"], indent=2))
        
        return "\n".join(combined_context)
    
    async def _generate_enhanced_response(self, question: str, enhanced_context: str, rag_result: Dict[str, Any]) -> Dict[str, Any]:
        """Generate response using enhanced context"""
        
        # Create enhanced prompt that includes both documentation and real-time data
        enhanced_prompt = f"""
        You are an AWS troubleshooting expert. Use both the documentation context and real-time AWS data to provide comprehensive troubleshooting guidance.
        
        Question: {question}
        
        Context:
        {enhanced_context}
        
        Please provide:
        1. Immediate assessment of the current situation based on real-time data
        2. Step-by-step troubleshooting guidance from documentation
        3. Specific recommendations based on the observed metrics/logs/status
        4. Preventive measures to avoid similar issues
        """
        
        # Use the existing LLM to generate response
        try:
            response = self.query_engine.llm.invoke(enhanced_prompt)
            enhanced_answer = response.content if hasattr(response, 'content') else str(response)
        except Exception as e:
            logger.error(f"LLM invocation failed: {e}")
            enhanced_answer = "Unable to generate enhanced response due to LLM error."
        
        # Return enhanced result
        enhanced_result = rag_result.copy()
        enhanced_result.update({
            "answer": enhanced_answer,
            "enhanced_with_mcp": True,
            "aws_real_time_data": True,
            "troubleshooting_mode": True
        })
        
        return enhanced_result

# Integration with existing FastAPI endpoints
class MCPEnhancedQueryEngine:
    """Enhanced query engine that integrates MCP capabilities"""
    
    def __init__(self, base_query_engine: QueryEngine):
        self.base_engine = base_query_engine
        self.mcp_integration = MCPRAGIntegration(base_query_engine)
        self._mcp_started = False
    
    async def initialize(self):
        """Initialize MCP integration"""
        if not self._mcp_started:
            success = await self.mcp_integration.start_mcp_server()
            self._mcp_started = success
            return success
        return True
    
    async def query_with_mcp(self, question: str, retrieval_config: Dict[str, Any] = None) -> Dict[str, Any]:
        """Query with MCP enhancement for troubleshooting"""
        
        # Check if this is a troubleshooting, inventory, or cost query
        troubleshooting_terms = [
            "error", "issue", "problem", "fail", "slow", "down", "not working",
            "troubleshoot", "debug", "fix", "broken", "performance"
        ]

        inventory_terms = [
            "how many", "list all", "show me", "what instances", "what services",
            "current status", "running instances", "inventory", "count"
        ]

        cost_terms = [
            "cost", "costs", "billing", "spend", "spending", "expensive", "price",
            "budget", "bill", "charges", "optimization", "optimize"
        ]

        question_lower = question.lower()
        is_troubleshooting = any(term in question_lower for term in troubleshooting_terms)
        is_inventory = any(term in question_lower for term in inventory_terms)
        is_cost_query = any(term in question_lower for term in cost_terms)

        # Use MCP for troubleshooting, inventory, or cost queries
        should_use_mcp = is_troubleshooting or is_inventory or is_cost_query
        
        if should_use_mcp and self._mcp_started:
            try:
                return await self.mcp_integration.enhanced_troubleshooting_query(question)
            except Exception as e:
                logger.error(f"MCP-enhanced query failed, falling back to regular RAG: {e}")
        
        # Fallback to regular RAG query
        return self.base_engine.query_advanced(question, retrieval_config)
    
    async def cleanup(self):
        """Cleanup MCP resources"""
        if self._mcp_started:
            await self.mcp_integration.stop_mcp_server()
            self._mcp_started = False
