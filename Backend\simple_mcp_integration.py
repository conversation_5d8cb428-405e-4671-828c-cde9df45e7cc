"""
Simplified MCP Integration for AWS RAG System
Direct AWS API integration without complex subprocess communication
Based on AWS MCP patterns from awslabs/mcp repository
"""

import asyncio
import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import boto3
import os

logger = logging.getLogger(__name__)

class SimpleMCPIntegration:
    """Simplified MCP integration using direct AWS API calls"""
    
    def __init__(self):
        self.aws_session = None
        self.ec2_client = None
        self.cloudwatch_client = None
        self.s3_client = None
        self.lambda_client = None
        self.rds_client = None
        self._initialized = False
        
    async def initialize(self):
        """Initialize AWS clients for MCP functionality"""
        try:
            # Initialize AWS session
            self.aws_session = boto3.Session(
                aws_access_key_id=os.getenv("AWS_ACCESS_KEY_ID"),
                aws_secret_access_key=os.getenv("AWS_SECRET_ACCESS_KEY"),
                region_name=os.getenv("AWS_REGION", "ap-south-1")
            )
            
            # Initialize AWS service clients
            self.ec2_client = self.aws_session.client('ec2')
            self.cloudwatch_client = self.aws_session.client('cloudwatch')
            self.s3_client = self.aws_session.client('s3')
            self.lambda_client = self.aws_session.client('lambda')
            self.rds_client = self.aws_session.client('rds')
            
            # Test connectivity
            identity = self.aws_session.client('sts').get_caller_identity()
            logger.info(f"Simple MCP integration initialized for: {identity.get('Arn', 'Unknown')}")
            
            self._initialized = True
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Simple MCP integration: {e}")
            self._initialized = False
            return False
    
    def is_mcp_query(self, question: str) -> bool:
        """Detect if a query should use MCP enhancement"""
        question_lower = question.lower()
        
        # Troubleshooting keywords
        troubleshooting_terms = [
            "error", "issue", "problem", "fail", "slow", "down", "not working",
            "troubleshoot", "debug", "fix", "broken", "performance", "timeout",
            "high cpu", "memory", "latency", "500 error", "503 error", "502 error"
        ]
        
        # Inventory keywords
        inventory_terms = [
            "how many", "list all", "show me", "what instances", "what services",
            "current status", "running instances", "inventory", "count"
        ]
        
        # Cost keywords
        cost_terms = [
            "cost", "costs", "billing", "spend", "spending", "expensive", "price",
            "budget", "bill", "charges", "optimization", "optimize"
        ]
        
        return (any(term in question_lower for term in troubleshooting_terms) or
                any(term in question_lower for term in inventory_terms) or
                any(term in question_lower for term in cost_terms))
    
    async def get_aws_inventory(self) -> Dict[str, Any]:
        """Get comprehensive AWS resource inventory"""
        if not self._initialized:
            return {"error": "MCP not initialized"}
        
        try:
            inventory = {}
            
            # Get EC2 instances
            ec2_response = self.ec2_client.describe_instances()
            instances = []
            for reservation in ec2_response['Reservations']:
                for instance in reservation['Instances']:
                    if instance['State']['Name'] != 'terminated':
                        instances.append({
                            'InstanceId': instance['InstanceId'],
                            'InstanceType': instance['InstanceType'],
                            'State': instance['State']['Name'],
                            'LaunchTime': instance.get('LaunchTime'),
                            'Platform': instance.get('Platform', 'Linux'),
                            'PrivateIpAddress': instance.get('PrivateIpAddress'),
                            'PublicIpAddress': instance.get('PublicIpAddress')
                        })
            
            inventory['EC2'] = {
                'total_count': len(instances),
                'running_count': len([i for i in instances if i['State'] == 'running']),
                'instances': instances
            }
            
            # Get Lambda functions
            lambda_response = self.lambda_client.list_functions()
            functions = []
            for func in lambda_response['Functions']:
                functions.append({
                    'FunctionName': func['FunctionName'],
                    'Runtime': func['Runtime'],
                    'LastModified': func['LastModified'],
                    'MemorySize': func['MemorySize'],
                    'Timeout': func['Timeout']
                })
            
            inventory['Lambda'] = {
                'count': len(functions),
                'functions': functions
            }
            
            # Get S3 buckets
            s3_response = self.s3_client.list_buckets()
            buckets = [bucket['Name'] for bucket in s3_response['Buckets']]
            inventory['S3'] = {
                'count': len(buckets),
                'buckets': buckets
            }
            
            # Get RDS instances
            try:
                rds_response = self.rds_client.describe_db_instances()
                db_instances = []
                for db in rds_response['DBInstances']:
                    db_instances.append({
                        'DBInstanceIdentifier': db['DBInstanceIdentifier'],
                        'DBInstanceClass': db['DBInstanceClass'],
                        'DBInstanceStatus': db['DBInstanceStatus'],
                        'Engine': db['Engine']
                    })
                inventory['RDS'] = {
                    'count': len(db_instances),
                    'instances': db_instances
                }
            except Exception as e:
                inventory['RDS'] = {
                    'count': 0,
                    'instances': [],
                    'error': str(e)
                }
            
            return inventory
            
        except Exception as e:
            logger.error(f"Error getting AWS inventory: {e}")
            return {"error": str(e)}
    
    async def get_cloudwatch_metrics(self, instance_id: str = None) -> Dict[str, Any]:
        """Get CloudWatch metrics for EC2 instances"""
        if not self._initialized:
            return {"error": "MCP not initialized"}
        
        try:
            metrics = {}
            end_time = datetime.utcnow()
            start_time = end_time - timedelta(hours=1)
            
            # Get CPU utilization for all instances or specific instance
            if instance_id:
                dimensions = [{'Name': 'InstanceId', 'Value': instance_id}]
            else:
                dimensions = []
            
            cpu_response = self.cloudwatch_client.get_metric_statistics(
                Namespace='AWS/EC2',
                MetricName='CPUUtilization',
                Dimensions=dimensions,
                StartTime=start_time,
                EndTime=end_time,
                Period=300,
                Statistics=['Average', 'Maximum']
            )
            
            metrics['CPUUtilization'] = {
                'datapoints': len(cpu_response['Datapoints']),
                'latest_average': cpu_response['Datapoints'][-1]['Average'] if cpu_response['Datapoints'] else None,
                'latest_maximum': cpu_response['Datapoints'][-1]['Maximum'] if cpu_response['Datapoints'] else None
            }
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error getting CloudWatch metrics: {e}")
            return {"error": str(e)}
    
    async def get_cost_estimates(self) -> Dict[str, Any]:
        """Get cost estimates based on running resources"""
        if not self._initialized:
            return {"error": "MCP not initialized"}
        
        try:
            cost_info = {
                "note": "Cost estimates based on current resource usage",
                "estimates": {}
            }
            
            # Get running EC2 instances for cost estimation
            inventory = await self.get_aws_inventory()
            
            if 'EC2' in inventory:
                running_instances = inventory['EC2']['running_count']
                cost_info['estimates']['EC2'] = {
                    'running_instances': running_instances,
                    'estimated_hourly_cost': f"${running_instances * 0.10:.2f}",  # Rough estimate
                    'note': 'Actual costs depend on instance types and usage patterns'
                }
            
            if 'Lambda' in inventory:
                function_count = inventory['Lambda']['count']
                cost_info['estimates']['Lambda'] = {
                    'function_count': function_count,
                    'note': 'Costs based on invocations and execution duration'
                }
            
            if 'S3' in inventory:
                bucket_count = inventory['S3']['count']
                cost_info['estimates']['S3'] = {
                    'bucket_count': bucket_count,
                    'note': 'Costs based on storage usage and requests'
                }
            
            return cost_info
            
        except Exception as e:
            logger.error(f"Error getting cost estimates: {e}")
            return {"error": str(e)}
    
    async def enhance_query_with_aws_data(self, question: str) -> Dict[str, Any]:
        """Enhance a query with real-time AWS data"""
        if not self._initialized:
            return {
                "enhanced_with_mcp": False,
                "aws_real_time_data": False,
                "error": "MCP not initialized"
            }
        
        try:
            aws_data = {}
            question_lower = question.lower()
            
            # Determine what data to fetch based on question
            if any(term in question_lower for term in ["how many", "list", "inventory", "instances", "services"]):
                aws_data["inventory"] = await self.get_aws_inventory()
            
            if any(term in question_lower for term in ["cost", "billing", "spend", "price"]):
                aws_data["costs"] = await self.get_cost_estimates()
            
            if any(term in question_lower for term in ["performance", "cpu", "slow", "metrics"]):
                # Extract instance ID if mentioned
                instance_id = None
                words = question.split()
                for word in words:
                    if word.startswith('i-') and len(word) > 10:
                        instance_id = word
                        break
                
                aws_data["metrics"] = await self.get_cloudwatch_metrics(instance_id)
            
            return {
                "enhanced_with_mcp": True,
                "aws_real_time_data": True,
                "troubleshooting_mode": True,
                "aws_data": aws_data
            }
            
        except Exception as e:
            logger.error(f"Error enhancing query with AWS data: {e}")
            return {
                "enhanced_with_mcp": False,
                "aws_real_time_data": False,
                "error": str(e)
            }

# Global instance
simple_mcp = SimpleMCPIntegration()
